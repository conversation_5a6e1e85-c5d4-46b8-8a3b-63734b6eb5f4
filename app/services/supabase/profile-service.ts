import { supabase } from "@/supabase"
import type { UserProfile } from "@/screens/EditProfile/types"

export class SupabaseProfileService {
  /**
   * Fetch the authenticated user's profile data from public.users.
   */
  static async fetchCurrentUserProfile(): Promise<UserProfile | null> {
    // Ensure we have an authenticated user session
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession()

    if (sessionError) throw sessionError
    const user = session?.user
    if (__DEV__) {
      console.log("[ProfileService] current session user:", user)
    }
    if (!user) throw new Error("No authenticated user found.")

    const { data, error } = await supabase
      .from("users")
      .select(
        `display_name, email, country_code, mobile_no, gender, dob, description, location`
      )
      .eq("user_id", user.id)
      .maybeSingle()

    if (error) throw error

    if (__DEV__) {
      console.log("[ProfileService] fetched user profile row:", data)
    }

    if (!data) return null

    return {
      name: data.display_name ?? "",
      email: data.email ?? "",
      countryCode: data.country_code ?? "",
      phone: data.mobile_no ?? "",
      gender: data.gender ?? "",
      dateOfBirth: data.dob ? new Date(data.dob) : new Date(),
      description: data.description ?? "",
      location: data.location ?? "",
      // avatar intentionally omitted; generated on-device
    }
  }

  /**
   * Update the authenticated user's profile row.
   */
  static async updateCurrentUserProfile(profile: UserProfile): Promise<void> {
    // Ensure authenticated
    const {
      data: { session: updateSession },
      error: sessionError2,
    } = await supabase.auth.getSession()
    if (sessionError2) throw sessionError2

    const user = updateSession?.user
    if (!user) throw new Error("No authenticated user found.")

    const { error } = await supabase
      .from("users")
      .update({
        display_name: profile.name,
        email: profile.email,
        country_code: profile.countryCode,
        mobile_no: profile.phone,
        gender: profile.gender,
        dob: profile.dateOfBirth.toISOString().split("T")[0], // YYYY-MM-DD
        description: profile.description,
        location: profile.location,
        last_updated: new Date().toISOString(),
      })
      .eq("user_id", user.id)

    if (error) throw error
  }

  /**
   * Fetch the authenticated user's statistics from public.user_stats.
   * Returns the stats object or null if not found.
   */
  static async fetchStats(): Promise<{
    totalMatches: number
    socialWins: number
    tournamentWins: number
  } | null> {
    // Ensure authenticated
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession()

    if (sessionError) throw sessionError
    const user = session?.user
    if (!user) throw new Error("No authenticated user found.")

    const { data, error } = await supabase
      .from("user_stats")
      .select("total_matches, social_wins, tournament_wins")
      .eq("user_id", user.id)
      .maybeSingle()

    if (error) throw error

    if (__DEV__) {
      console.log("[ProfileService] fetched user stats row:", data)
    }

    if (!data) return null

    return {
      totalMatches: data.total_matches ?? 0,
      socialWins: data.social_wins ?? 0,
      tournamentWins: data.tournament_wins ?? 0,
    }
  }
}