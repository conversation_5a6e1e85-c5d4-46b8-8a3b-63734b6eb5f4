import { v4 as uuidv4 } from 'uuid';

interface UserData {
  id: string;
  profile: {
    id: string;
    name: string;
    description: string;
    avatar: string;
    initials: string;
  };
  stats: {
    totalMatches: number;
    socialWins: number;
    tournamentWins: number;
  };
  upcomingEvents: any[];
  pastEvents: any[];
  communities: any[];
  trophies: any[];
}

interface User {
  user_id: string;
  name: string;
  surname: string;
  email: string;
  country_code: string;
  mobile_no: string;
  gender: string;
  dob: string;
  description: string;
  location: string;
  trophies: number;
  created_at: string;
  last_updated: string;
}

const existingUserData = require('../../data/user_data.json').users;

const generateDummyUser = (index: number): User => {
  const firstName = `Dummy${index}`;
  const lastName = `User${index}`;
  const email = `dummy${index}@example.com`;
  const mobileNo = `+123456789${String(index).padStart(2, '0')}`;
  const dob = `1990-01-${String(index + 1).padStart(2, '0')}`;
  const gender = index % 2 === 0 ? 'Male' : 'Female';
  const location = `City ${index}, Country ${index % 5}`;
  const description = `Enthusiastic padel player number ${index}`;
  const trophies = Math.floor(Math.random() * 10);

  return {
    user_id: uuidv4(),
    name: firstName,
    surname: lastName,
    email: email,
    country_code: 'US',
    mobile_no: mobileNo,
    gender: gender,
    dob: dob,
    description: description,
    location: location,
    trophies: trophies,
    created_at: 'NOW',
    last_updated: 'NOW',
  };
};

export const generateUsersForDb = (): User[] => {
  const users: User[] = [];
  const now = new Date().toISOString();

  // Add existing users from user_data.json
  for (const key in existingUserData) {
    const userData: UserData = existingUserData[key];
    const [firstName, ...rest] = userData.profile.name.split(' ');
    const lastName = rest.join(' ');

    users.push({
      user_id: userData.id,
      name: firstName,
      surname: lastName,
      email: `${firstName.toLowerCase()}.${lastName.toLowerCase().replace(/\s/g, '')}@example.com`,
      country_code: 'US', // Placeholder
      mobile_no: '+1234567890', // Placeholder
      gender: 'Unknown', // Placeholder
      dob: '2000-01-01', // Placeholder
      description: userData.profile.description,
      location: 'Unknown', // Placeholder
      trophies: userData.trophies ? userData.trophies.length : 0,
      created_at: now,
      last_updated: now,
    });
  }

  // Generate additional dummy users to reach 30
  const currentCount = users.length;
  for (let i = 0; i < 30 - currentCount; i++) {
    users.push(generateDummyUser(i + currentCount));
  }

  return users;
};
