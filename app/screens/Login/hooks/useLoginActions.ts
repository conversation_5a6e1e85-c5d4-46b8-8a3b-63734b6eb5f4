import { useEffect, useRef, useState } from "react"
import { TextInput } from "react-native"
import { useStores } from "app/models"
import { SupabaseAuthService } from "@/services/supabase/auth-service"
import { SupabaseProfileService } from "@/services/supabase/profile-service"

export const useLoginActions = () => {
  const authPasswordInput = useRef<TextInput>(null)

  const [authPassword, setAuthPassword] = useState("")
  const [isAuthPasswordHidden, setIsAuthPasswordHidden] = useState(true)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [attemptsCount, setAttemptsCount] = useState(0)
  const {
    authenticationStore: { authEmail, setAuthEmail, setAuthToken, validationError, setCurrentUserId },
    userProfileStore,
  } = useStores()

  // Prefill credentials in development to speed up testing (optional)
  useEffect(() => {
    if (__DEV__) {
      setAuthEmail("")
      setAuthPassword("")
    }

    return () => {
      setAuthPassword("")
      setAuthEmail("")
    }
  }, [setAuthEmail])

  const error = isSubmitted ? validationError : ""

  async function login() {
    setIsSubmitted(true)
    setAttemptsCount(attemptsCount + 1)

    if (validationError) return

    try {
      const { session, user } = await SupabaseAuthService.signInWithEmailPassword(
        authEmail,
        authPassword,
      )

      if (session?.access_token && user) {
        setAuthToken(session.access_token)
        setCurrentUserId(user.id)

        // Fetch profile row and cache
        SupabaseProfileService.fetchCurrentUserProfile()
          .then((profile) => {
            if (profile) userProfileStore.setProfile({ userId: user.id, displayName: profile.name, email: profile.email, countryCode: profile.countryCode, phone: profile.phone, gender: profile.gender, dateOfBirth: profile.dateOfBirth, description: profile.description, location: profile.location })
          })
          .catch(console.error)

        // Fetch and cache user statistics
        SupabaseProfileService.fetchStats()
          .then((stats) => {
            if (stats) {
              userProfileStore.setStats(stats)
            }
          })
          .catch(console.error)

        // Clean up sensitive data
        setAuthPassword("")
        setAuthEmail("")
      }
      setIsSubmitted(false)
    } catch (e: any) {
      console.error("Login failed", e)
      // You could surface this to the UI via a separate state variable
      // but for now we'll just increment attempts so the helper text shows.
    }
  }

  return {
    authPasswordInput,
    authPassword,
    setAuthPassword,
    isAuthPasswordHidden,
    setIsAuthPasswordHidden,
    isSubmitted,
    setIsSubmitted,
    attemptsCount,
    setAttemptsCount,
    authEmail,
    setAuthEmail,
    validationError,
    setAuthToken,
    setCurrentUserId,
    error,
    login,
  }
}
