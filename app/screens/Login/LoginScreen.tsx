import { observer } from "mobx-react-lite"
import { FC, ComponentType } from "react"
import { TextInput, TextStyle, ViewStyle } from "react-native"
import { Button, Screen, Text, TextField, TextFieldAccessoryProps } from "app/components"
import { DeepLinkDebugger } from "app/components/DeepLinkDebugger"
import { useAppTheme } from "@/utils/useAppTheme"
import { LoginScreenProps, LoginScreenStyles } from "./types"
import { useLoginActions } from "./hooks/useLoginActions"
import { PasswordRightAccessory } from "./components/PasswordRightAccessory"

export const LoginScreen: FC<LoginScreenProps> = observer(function LoginScreen(_props) {
  const {
    authPasswordInput,
    authPassword,
    setAuthPassword,
    isAuthPasswordHidden,
    setIsAuthPasswordHidden,
    attemptsCount,
    authEmail,
    setAuthEmail,
    error,
    login,
  } = useLoginActions()

  const { themed } = useAppTheme()

  const RightAccessory: ComponentType<TextFieldAccessoryProps> = (props) => (
    <PasswordRightAccessory
      {...props}
      isAuthPasswordHidden={isAuthPasswordHidden}
      setIsAuthPasswordHidden={setIsAuthPasswordHidden}
    />
  )

  return (
    <Screen
      preset="auto"
      contentContainerStyle={themed($screenContentContainer)}
      safeAreaEdges={["top", "bottom"]}
    >
      <Text testID="login-heading" tx="loginScreen:logIn" preset="heading" style={themed($logIn)} />
      <Text tx="loginScreen:enterDetails" preset="subheading" style={themed($enterDetails)} />
      {attemptsCount > 2 && (
        <Text tx="loginScreen:hint" size="sm" weight="light" style={themed($hint)} />
      )}

      <TextField
        value={authEmail}
        onChangeText={setAuthEmail}
        containerStyle={themed($textField)}
        autoCapitalize="none"
        autoComplete="email"
        autoCorrect={false}
        keyboardType="email-address"
        labelTx="loginScreen:emailFieldLabel"
        placeholderTx="loginScreen:emailFieldPlaceholder"
        helper={error}
        status={error ? "error" : undefined}
        onSubmitEditing={() => authPasswordInput.current?.focus()}
      />

      <TextField
        ref={authPasswordInput}
        value={authPassword}
        onChangeText={setAuthPassword}
        containerStyle={themed($textField)}
        autoCapitalize="none"
        autoComplete="password"
        autoCorrect={false}
        secureTextEntry={isAuthPasswordHidden}
        labelTx="loginScreen:passwordFieldLabel"
        placeholderTx="loginScreen:passwordFieldPlaceholder"
        onSubmitEditing={login}
        RightAccessory={RightAccessory}
      />

      <Button
        testID="login-button"
        tx="loginScreen:tapToLogIn"
        style={themed($tapButton)}
        preset="reversed"
        onPress={login}
      />

      {/* Navigate to Sign Up */}
      <Text
        text="Don't have an account? Sign Up"
        size="sm"
        weight="light"
        style={{ marginTop: 16, textAlign: "center" }}
        onPress={() => _props.navigation?.navigate?.("SignUp")}
      />

      {/* Navigate to Reset Password */}
      <Text
        text="Forgot password? Click here"
        size="sm"
        weight="light"
        style={{ marginTop: 8, textAlign: "center" }}
        onPress={() => _props.navigation?.navigate?.("ResetPassword")}
      />

      {/* Debug tools for deep link testing */}
      <DeepLinkDebugger />
    </Screen>
  )
})

import {
  $screenContentContainer,
  $logIn,
  $enterDetails,
  $hint,
  $textField,
  $tapButton
} from "./styles"
