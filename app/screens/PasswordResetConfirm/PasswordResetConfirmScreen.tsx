import { observer } from "mobx-react-lite"
import { FC, useState, useEffect } from "react"
import { View, ViewStyle, TouchableOpacity, TextStyle, Linking } from "react-native"
import { Screen, Text, TextField, Button, Icon } from "@/components"
import { AppStackScreenProps } from "@/navigators"
import { useHeader } from "@/utils/useHeader"
import { SupabaseAuthService } from "@/services/supabase/auth-service"
import { showErrorToast, showSuccessToast, showWarningToast } from "@/utils/toast"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { logDeepLinkEvent } from "@/utils/deepLinkDebugger"
import { useStores } from "@/models"

interface PasswordResetConfirmScreenProps extends AppStackScreenProps<"PasswordResetConfirm"> {}

export const PasswordResetConfirmScreen: FC<PasswordResetConfirmScreenProps> = observer(function PasswordResetConfirmScreen({ navigation }) {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  const { authenticationStore } = useStores()
  const [password, setPassword] = useState("")
  const [loading, setLoading] = useState(false)

  // Debug: Log when this screen is mounted and check for URL parameters
  useEffect(() => {
    const navigationState = navigation.getState()

    logDeepLinkEvent('SCREEN_MOUNT', {
      screen: 'PasswordResetConfirm',
      navigationState,
      routeParams: navigationState?.routes?.find(r => r.name === 'PasswordResetConfirm')?.params,
    })

    // Check current URL for debugging
    Linking.getInitialURL().then((url) => {
      if (url) {
        logDeepLinkEvent('SCREEN_MOUNT', {
          screen: 'PasswordResetConfirm',
          currentUrl: url,
        })
      }
    })
  }, [])

  // Handle back navigation - logout and go to login
  const handleBackNavigation = async () => {
    console.log("🔙 [Password Reset] Back button pressed, logging out and going to Login")

    try {
      // Log out from auth store
      authenticationStore.logout()

      // Sign out from Supabase
      const { supabase } = await import('@/supabase')
      await supabase.auth.signOut()

      console.log("🔙 [Password Reset] Logout complete")
    } catch (error) {
      console.error("❌ [Password Reset] Error during logout:", error)
      // Force logout even if Supabase fails
      authenticationStore.logout()
    }
  }

  // Header: back arrow that properly handles logout
  useHeader({
    leftContent: (
      <TouchableOpacity onPress={handleBackNavigation}>
        <Icon icon="caretLeft" size={24} color={colors.text} />
      </TouchableOpacity>
    ),
    backgroundColor: "transparent",
    includeSafeArea: true,
  })

  // Validation rules
  const hasMinChars = password.length >= 8
  const hasDigit = /\d/.test(password)
  const hasUppercase = /[A-Z]/.test(password)

  const allValid = hasMinChars && hasDigit && hasUppercase

  const handleReset = async () => {
    if (!allValid) {
      showWarningToast("Password must be at least 8 chars, include a digit and uppercase letter.")
      return
    }

    try {
      setLoading(true)
      await SupabaseAuthService.updateUserPassword(password)
      showSuccessToast("Password reset successfully. You can now log in.")

      console.log("🔐 [Password Reset] Password updated successfully, logging out user")

      // Use the same logout logic as the back button
      await handleBackNavigation()

    } catch (error: any) {
      showErrorToast(error?.message ?? "Failed to reset password")
    } finally {
      setLoading(false)
    }
  }

  return (
    <Screen preset="scroll" safeAreaEdges={["bottom"]} contentContainerStyle={themed($container)}>
      {/* Page Heading */}
      <Text text="Reset Password" preset="heading" style={themed($heading)} />
      <Text
        text="Please enter a new password for your account."
        size="sm"
        style={themed($subheading)}
      />

      <TextField
        placeholder="New Password"
        secureTextEntry
        value={password}
        onChangeText={setPassword}
        containerStyle={themed($inputContainer)}
      />

      {/* Validation rules */}
      <View style={themed($rulesContainer)}>
        {[
          { label: "Minimum 8 characters", valid: hasMinChars },
          { label: "At least 1 digit", valid: hasDigit },
          { label: "At least 1 uppercase", valid: hasUppercase },
        ].map((rule, idx) => (
          <View key={idx} style={themed($ruleRow)}>
            <Icon
              icon={rule.valid ? "check" : "x"}
              size={12}
              color={rule.valid ? colors.success ?? "green" : colors.error}
            />
            <Text
              text={rule.label}
              size="sm"
              style={themed([$ruleText, { color: rule.valid ? colors.text : colors.error }])}
            />
          </View>
        ))}
      </View>

      <Button
        text="Reset Password"
        onPress={handleReset}
        disabled={loading}
        preset="filled"
        style={themed($saveButton)}
      />
    </Screen>
  )
})

/* ──────────────────────────────────────────────────────────
 * Styles
 * ─────────────────────────────────────────────────────────*/
const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  padding: spacing.lg,
})

const $heading: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.sm,
})

const $subheading: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.md,
})

const $inputContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.md,
})

const $rulesContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.lg,
})

const $ruleRow: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.xs,
  marginBottom: spacing.xs,
})

const $ruleText: ThemedStyle<TextStyle> = () => ({})

const $saveButton: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginTop: spacing.lg,
}) 