import { getUserData, isCurrentUser } from "../utils"
import { useStores } from "@/models"

interface UseProfileDataProps {
  userId?: string
}

export const useProfileData = ({ userId }: UseProfileDataProps) => {
  // Get profile data and user context
  const store = useStores()
  const profileData = getUserData(userId)
  const currentUser = isCurrentUser(userId)

  // If this is the current user, override stats with cached store values
  if (currentUser) {
    profileData.stats = {
      totalMatches: store.userProfileStore.totalMatches,
      socialWins: store.userProfileStore.socialWins,
      tournamentWins: store.userProfileStore.tournamentWins,
    }

    // Also override core profile fields if available
    profileData.profile.name = store.userProfileStore.displayName || profileData.profile.name
    profileData.profile.description = store.userProfileStore.description || profileData.profile.description
  }

  return {
    // Data only
    profileData,
    isCurrentUser: currentUser,
  }
}
