export interface UserProfile {
  id: string
  name: string
  description: string
  avatar: string
  initials: string
}

export interface UserStats {
  totalMatches: number
  socialWins: number
  tournamentWins: number
}

export interface ProfileEvent {
  id: number
  type: string
  title: string
  date: string
  day: string
  location: string
  price: string
  participants: number
  maxParticipants: number
  status: "upcoming" | "completed" | "joinable" | "finished"
}

export interface UserCommunity {
  id: string
  name: string
  members: number
  role: "Member" | "Admin"
  color: string
}

export interface Trophy {
  id: number
  title: string
  description: string
  date: string
  icon: string
}

export type ProfileTab = "matches" | "communities" | "trophies"

export interface ProfileData {
  profile: UserProfile
  stats: UserStats
  upcomingEvents: ProfileEvent[]
  pastEvents: ProfileEvent[]
  communities: UserCommunity[]
  trophies: Trophy[]
}

export interface ProfileHeaderProps {
  profile: UserProfile
  stats: UserStats
  activeTab: ProfileTab
  onTabChange: (tab: ProfileTab) => void
  isCurrentUser?: boolean
  onMessagePress?: () => void
}

export interface ProfileListProps {
  activeTab: ProfileTab
  upcomingEvents: ProfileEvent[]
  pastEvents: ProfileEvent[]
  communities: UserCommunity[]
  trophies: Trophy[]
  onEventPress: (event: ProfileEvent) => void
  onCommunityPress: (community: UserCommunity) => void
  onEventDetailPress?: (event: ProfileEvent) => void
}
