import { forwardRef } from "react"
import { ScrollView, ViewStyle } from "react-native"
import { ProfileListProps } from "../types"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { Matches } from "@/screens/Profile/components/Matches"
import { Communities } from "@/screens/Profile/components/Communities"
import { Trophies } from "@/screens/Profile/components/Trophies"

export const ProfileList = forwardRef<ScrollView, ProfileListProps>(
  (
    {
      activeTab,
      upcomingEvents,
      pastEvents,
      communities,
      trophies,
      onEventPress,
      onCommunityPress,
      onEventDetailPress,
    },
    ref,
  ) => {
    const { themed } = useAppTheme()

    const renderContent = () => {
      switch (activeTab) {
        case "matches":
          return (
            <Matches
              upcomingEvents={upcomingEvents}
              pastEvents={pastEvents}
              onEventPress={onEventPress}
              onEventDetailPress={onEventDetailPress}
            />
          )
        case "communities":
          return <Communities communities={communities} onCommunityPress={onCommunityPress} />
        case "trophies":
          return <Trophies trophies={trophies} />
        default:
          return null
      }
    }

    return (
      <ScrollView
        ref={ref}
        style={themed($container)}
        contentContainerStyle={themed($scrollContent)}
        showsVerticalScrollIndicator={false}
      >
        {renderContent()}
      </ScrollView>
    )
  },
)

ProfileList.displayName = "ProfileList"

const $container: ThemedStyle<ViewStyle> = ({ colors }) => ({
  flex: 1,
  backgroundColor: colors.background,
})

const $scrollContent: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.lg,
  paddingBottom: spacing.xl,
})
