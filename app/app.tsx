/* eslint-disable import/first */
// Polyfill for crypto.getRandomValues required by uuid (used in Google Places autocomplete)
import "react-native-get-random-values"
/**
 * Welcome to the main entry point of the app. In this file, we'll
 * be kicking off our app.
 *
 * Most of this file is boilerplate and you shouldn't need to modify
 * it very often. But take some time to look through and understand
 * what is going on here.
 *
 * The app navigation resides in ./app/navigators, so head over there
 * if you're interested in adding screens and navigators.
 */
if (__DEV__) {
  // Load Reactotron in development only.
  // Note that you must be using metro's `inlineRequires` for this to work.
  // If you turn it off in metro.config.js, you'll have to manually import it.
  require("./devtools/ReactotronConfig.ts")
}
import "./utils/gestureHandler"
import { initI18n } from "./i18n"
import { useFonts } from "expo-font"
import { useEffect, useState } from "react"
import { initialWindowMetrics, SafeAreaProvider } from "react-native-safe-area-context"
import { GestureHandlerRootView } from "react-native-gesture-handler"
import * as Linking from "expo-linking"
import * as SplashScreen from "expo-splash-screen"
import { useInitialRootStore } from "./models"
import { AppNavigator, useNavigationPersistence } from "./navigators"
import * as storage from "./utils/storage"
import { customFontsToLoad } from "./theme"
import { KeyboardProvider } from "react-native-keyboard-controller"
import { loadDateFnsLocale } from "./utils/formatDate"
import { preloadIcons } from "./utils/iconPreloader"
import { LogBox } from "react-native"
import ToastManager from "toastify-react-native"
import { logDeepLinkEvent, isPasswordResetFlow, extractAuthTokensFromUrl, processPasswordResetUrl } from "./utils/deepLinkDebugger"

export const NAVIGATION_PERSISTENCE_KEY = "NAVIGATION_STATE"

// Web / deep linking configuration
// createURL("/") → padelcommunityapp:/// (3 slashes)
// createURL("") → padelcommunityapp://  (2 slashes)
const prefix = Linking.createURL("/")
const prefixNoSlash = Linking.createURL("")

const config = {
  screens: {
    // Deep-link for password-reset flow
    PasswordResetConfirm: "PasswordResetConfirm",
    Login: {
      path: "",
    },
    Main: "main",
    Events: "events",
    Communities: "communities",
    Profile: "profile",
    CommunityDetail: "community/:communityId",
    EventDetail: "event/:eventId",
    Notifications: "notifications",
    PrivateChat: "chat/:participantId",
  },
}

// Ignore verbose Reanimated shared value warnings in dev
if (__DEV__) {
  LogBox.ignoreLogs([/\[Reanimated\] Reading from `value` during component render/])
}

/**
 * This is the root component of our app.
 * @param {AppProps} props - The props for the `App` component.
 * @returns {JSX.Element} The rendered `App` component.
 */
export function App() {
  const {
    initialNavigationState,
    onNavigationStateChange,
    isRestored: isNavigationStateRestored,
  } = useNavigationPersistence(storage, NAVIGATION_PERSISTENCE_KEY)

  const [areFontsLoaded, fontLoadError] = useFonts(customFontsToLoad)
  const [isI18nInitialized, setIsI18nInitialized] = useState(false)

  useEffect(() => {
    initI18n()
      .then(() => setIsI18nInitialized(true))
      .then(() => loadDateFnsLocale())
      .then(() => preloadIcons()) // Preload ALL icons during app initialization
  }, [])

  // Deep link debugging and manual auth processing
  useEffect(() => {
    const handleDeepLink = async (url: string) => {
      const tokens = extractAuthTokensFromUrl(url)
      const isPasswordReset = isPasswordResetFlow(url)

      logDeepLinkEvent('URL_RECEIVED', {
        url,
        isPasswordReset,
        tokens: tokens ? { ...tokens, access_token: tokens.access_token ? '[REDACTED]' : null } : null,
      })

      // Manual auth processing for password reset URLs
      if (isPasswordReset && tokens?.access_token && tokens?.type === 'recovery') {
        // Use the dedicated function for processing password reset URLs
        processPasswordResetUrl(url)
      }
    }

    // Listen for deep links when app is already open
    const subscription = Linking.addEventListener("url", (event) => {
      handleDeepLink(event.url)
    })

    // Check for initial URL when app is opened via deep link
    Linking.getInitialURL().then((url) => {
      if (url) {
        handleDeepLink(url)
      }
    })

    return () => subscription?.remove()
  }, [])

  const { rehydrated } = useInitialRootStore(() => {
    // This runs after the root store has been initialized and rehydrated.

    // If your initialization scripts run very fast, it's good to show the splash screen for just a bit longer to prevent flicker.
    // Slightly delaying splash screen hiding for better UX; can be customized or removed as needed,
    setTimeout(SplashScreen.hideAsync, 500)
  })

  // Before we show the app, we have to wait for our state to be ready.
  // In the meantime, don't render anything. This will be the background
  // color set in native by rootView's background color.
  // In iOS: application:didFinishLaunchingWithOptions:
  // In Android: https://stackoverflow.com/a/45838109/204044
  // You can replace with your own loading component if you wish.
  if (
    !rehydrated ||
    !isNavigationStateRestored ||
    !isI18nInitialized ||
    (!areFontsLoaded && !fontLoadError)
  ) {
    return null
  }

  const linking = {
    prefixes: [prefix, prefixNoSlash],
    config,
  }

  // Enhanced navigation state change handler with debugging
  const handleNavigationStateChange = (state: any) => {
    const currentRoute = state?.routes?.[state?.index]?.name
    const routeParams = state?.routes?.[state?.index]?.params

    logDeepLinkEvent('NAVIGATION_CHANGE', {
      currentRoute,
      routeParams,
      isPasswordResetScreen: currentRoute === 'PasswordResetConfirm',
    })

    onNavigationStateChange(state)
  }

  // otherwise, we're ready to render the app
  return (
    <SafeAreaProvider initialMetrics={initialWindowMetrics}>
      {/* Return two siblings, so wrap in fragment */}
      <>
        <GestureHandlerRootView style={{ flex: 1 }}>
          <KeyboardProvider>
            <AppNavigator
              linking={linking}
              initialState={initialNavigationState}
              onStateChange={handleNavigationStateChange}
            />
          </KeyboardProvider>
        </GestureHandlerRootView>
        {/* Toast notifications provider */}
        <ToastManager position="top" showCloseIcon showProgressBar useModal={false} />
      </>
    </SafeAreaProvider>
  )
}
