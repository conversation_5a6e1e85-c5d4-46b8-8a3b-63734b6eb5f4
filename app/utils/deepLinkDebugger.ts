/**
 * Deep Link Debugging Utility
 * 
 * This utility helps track and debug deep link flows in the app.
 * It provides centralized logging and state tracking for deep link events.
 */

interface DeepLinkEvent {
  type: 'URL_RECEIVED' | 'AUTH_STATE_CHANGE' | 'NAVIGATION_CHANGE' | 'SCREEN_MOUNT'
  data: any
  timestamp: string
}

class DeepLinkDebugger {
  private events: DeepLinkEvent[] = []
  private isEnabled = __DEV__ // Only enable in development

  /**
   * Log a deep link related event
   */
  log(type: DeepLinkEvent['type'], data: any) {
    if (!this.isEnabled) return

    const event: DeepLinkEvent = {
      type,
      data,
      timestamp: new Date().toISOString(),
    }

    this.events.push(event)
    
    // Keep only last 50 events to prevent memory issues
    if (this.events.length > 50) {
      this.events = this.events.slice(-50)
    }

    // Log to console with emoji for easy identification
    const emoji = this.getEmojiForType(type)
    console.log(`${emoji} [DeepLink Debug] ${type}:`, data)
  }

  /**
   * Get all logged events
   */
  getEvents(): DeepLinkEvent[] {
    return [...this.events]
  }

  /**
   * Clear all logged events
   */
  clear() {
    this.events = []
    console.log("🧹 [DeepLink Debug] Events cleared")
  }

  /**
   * Print a summary of recent events
   */
  printSummary() {
    if (!this.isEnabled) return

    console.log("📊 [DeepLink Debug] Recent Events Summary:")
    console.log("=" * 50)
    
    this.events.slice(-10).forEach((event, index) => {
      const emoji = this.getEmojiForType(event.type)
      console.log(`${index + 1}. ${emoji} ${event.type} at ${event.timestamp}`)
      console.log(`   Data:`, event.data)
    })
    
    console.log("=" * 50)
  }

  private getEmojiForType(type: DeepLinkEvent['type']): string {
    switch (type) {
      case 'URL_RECEIVED': return '🔗'
      case 'AUTH_STATE_CHANGE': return '🔐'
      case 'NAVIGATION_CHANGE': return '🧭'
      case 'SCREEN_MOUNT': return '📱'
      default: return '🔍'
    }
  }
}

// Export singleton instance
export const deepLinkDebugger = new DeepLinkDebugger()

/**
 * Helper function to quickly log deep link events
 */
export const logDeepLinkEvent = (type: DeepLinkEvent['type'], data: any) => {
  deepLinkDebugger.log(type, data)
}

/**
 * Helper to check if we're in a password reset flow
 */
export const isPasswordResetFlow = (url?: string): boolean => {
  if (!url) return false
  return url.includes('PasswordResetConfirm') || url.includes('type=recovery')
}

/**
 * Extract auth tokens from URL
 */
export const extractAuthTokensFromUrl = (url: string) => {
  try {
    const urlObj = new URL(url)
    const fragment = urlObj.hash || urlObj.search

    const params = new URLSearchParams(fragment.replace('#', ''))

    return {
      access_token: params.get('access_token'),
      refresh_token: params.get('refresh_token'),
      type: params.get('type'),
      expires_in: params.get('expires_in'),
      expires_at: params.get('expires_at'),
      token_type: params.get('token_type'),
    }
  } catch (error) {
    console.warn("Failed to parse URL tokens:", error)
    return null
  }
}

/**
 * Manually process password reset deep link
 * This is a fallback when Supabase's automatic URL detection fails
 */
export const processPasswordResetUrl = async (url: string) => {
  const tokens = extractAuthTokensFromUrl(url)

  if (!tokens?.access_token || tokens.type !== 'recovery') {
    console.warn("🚫 [Deep Link] Invalid or missing recovery tokens")
    return false
  }

  try {
    console.log("🔧 [Deep Link] Processing password reset URL manually...")
    console.log("🔧 [Deep Link] Tokens found:", {
      hasAccessToken: !!tokens.access_token,
      hasRefreshToken: !!tokens.refresh_token,
      type: tokens.type,
      expiresIn: tokens.expires_in,
    })

    // Dynamic import to avoid circular dependencies
    const { supabase } = await import('@/supabase')

    // First, let's try to get the current session to see if Supabase already processed it
    const { data: currentSession } = await supabase.auth.getSession()
    console.log("🔍 [Deep Link] Current session before manual processing:", {
      hasSession: !!currentSession.session,
      userId: currentSession.session?.user?.id,
    })

    // Set the session manually
    const { data, error } = await supabase.auth.setSession({
      access_token: tokens.access_token,
      refresh_token: tokens.refresh_token || '',
    })

    if (error) {
      console.error("❌ [Deep Link] Failed to set session:", error)
      return false
    }

    console.log("✅ [Deep Link] Session set successfully:", {
      hasSession: !!data.session,
      userId: data.session?.user?.id,
      userEmail: data.session?.user?.email,
    })

    // Force navigation after a short delay to allow auth state to propagate
    setTimeout(async () => {
      try {
        const { navigationRef } = await import('@/navigators')
        if (navigationRef.current) {
          console.log("🧭 [Deep Link] Navigating to PasswordResetConfirm")
          navigationRef.current.navigate('PasswordResetConfirm' as never)
        } else {
          console.warn("⚠️ [Deep Link] Navigation ref not available")
        }
      } catch (navError) {
        console.error("❌ [Deep Link] Navigation failed:", navError)
      }
    }, 500) // Increased delay to ensure auth state propagates

    return true
  } catch (error) {
    console.error("❌ [Deep Link] Error processing password reset URL:", error)
    return false
  }
}
