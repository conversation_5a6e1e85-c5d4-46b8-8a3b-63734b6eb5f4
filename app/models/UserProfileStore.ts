import { Instance, SnapshotOut, types } from "mobx-state-tree"

export const UserProfileStoreModel = types
  .model("UserProfileStore", {
    userId: types.maybe(types.string),
    displayName: "",
    email: "",
    countryCode: types.maybe(types.string),
    phone: types.maybe(types.string),
    gender: types.maybe(types.string),
    dateOfBirth: types.maybe(types.Date),
    description: types.maybe(types.string),
    location: types.maybe(types.string),
    /**
     * Cached statistics for the authenticated user. These map 1-to-1 with the
     * columns in `public.user_stats`.
     */
    totalMatches: types.optional(types.number, 0),
    socialWins: types.optional(types.number, 0),
    tournamentWins: types.optional(types.number, 0),
  })
  .actions((store) => ({
    setProfile(data: Partial<typeof store>) {
      Object.assign(store, data)
    },
    clear() {
      store.userId = undefined
      store.displayName = ""
      store.email = ""
      store.countryCode = undefined
      store.phone = undefined
      store.gender = undefined
      store.dateOfBirth = undefined
      store.description = undefined
      store.location = undefined
      // Reset cached stats
      store.totalMatches = 0
      store.socialWins = 0
      store.tournamentWins = 0
    },
    setStats(data: {
      totalMatches?: number
      socialWins?: number
      tournamentWins?: number
    }) {
      if (typeof data.totalMatches === "number") store.totalMatches = data.totalMatches
      if (typeof data.socialWins === "number") store.socialWins = data.socialWins
      if (typeof data.tournamentWins === "number") store.tournamentWins = data.tournamentWins
    },
  }))

export interface UserProfileStore extends Instance<typeof UserProfileStoreModel> {}
export interface UserProfileStoreSnapshot extends SnapshotOut<typeof UserProfileStoreModel> {} 