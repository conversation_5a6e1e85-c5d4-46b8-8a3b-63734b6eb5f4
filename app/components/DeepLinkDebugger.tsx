import React from 'react'
import { View, ViewStyle } from 'react-native'
import { Button, Text } from '@/components'
import { deepLinkDebugger, processPasswordResetUrl } from '@/utils/deepLinkDebugger'
import { Linking } from 'react-native'

/**
 * Debug component for testing deep link functionality
 * Only renders in development mode
 */
export const DeepLinkDebugger: React.FC = () => {
  if (!__DEV__) return null

  const handlePrintSummary = () => {
    deepLinkDebugger.printSummary()
  }

  const handleClearEvents = () => {
    deepLinkDebugger.clear()
  }

  const handleTestPasswordReset = async () => {
    // Get the current URL and try to process it
    const url = await Linking.getInitialURL()
    if (url) {
      console.log("🧪 [Debug] Testing password reset with current URL:", url)
      await processPasswordResetUrl(url)
    } else {
      console.log("🧪 [Debug] No current URL found")
    }
  }

  const handleTestManualNavigation = async () => {
    try {
      const { navigationRef } = await import('@/navigators')
      if (navigationRef.current) {
        console.log("🧪 [Debug] Manually navigating to PasswordResetConfirm")
        navigationRef.current.navigate('PasswordResetConfirm' as never)
      }
    } catch (error) {
      console.error("🧪 [Debug] Manual navigation failed:", error)
    }
  }

  return (
    <View style={$container}>
      <Text text="🔧 Deep Link Debug Tools" size="sm" weight="bold" style={$title} />
      
      <Button
        text="Print Event Summary"
        onPress={handlePrintSummary}
        preset="default"
        style={$button}
      />
      
      <Button
        text="Clear Events"
        onPress={handleClearEvents}
        preset="default"
        style={$button}
      />
      
      <Button
        text="Test Password Reset"
        onPress={handleTestPasswordReset}
        preset="default"
        style={$button}
      />
      
      <Button
        text="Manual Navigation"
        onPress={handleTestManualNavigation}
        preset="default"
        style={$button}
      />
    </View>
  )
}

const $container: ViewStyle = {
  padding: 16,
  backgroundColor: '#f0f0f0',
  margin: 8,
  borderRadius: 8,
  borderWidth: 1,
  borderColor: '#ddd',
}

const $title: ViewStyle = {
  marginBottom: 12,
}

const $button: ViewStyle = {
  marginBottom: 8,
}
