# Deep Link & Auth Flow Debugging Summary

## 📅 Date
2025-07-10

## 🎯 **Objective**
To diagnose and resolve a critical issue where the password reset deep link opened the application but failed to navigate to the `PasswordResetConfirm` screen, leaving the user stuck on the `Login` screen.

## 🔑 **Problem Description**
When a user clicked the password reset link sent to their email, the app would launch correctly, but the navigation to the password reset screen would not occur. This indicated a failure in either the deep link handling, the authentication flow, or both. The debugging process involved several stages of discovery, revealing multiple interconnected issues.

## 🔬 **Debugging Journey & Key Discoveries**

The resolution required a multi-step investigation, uncovering several layers to the problem:

### **1. Initial Hypothesis: Missing Navigation Configuration**
- **Symptom:** The app opens, but no navigation occurs.
- **Action:** Added the `linking` prop to the `NavigationContainer` in `app/navigators/AppNavigator.tsx`.
- **Discovery:** This was a necessary step, but it revealed a **duplicate `linking` configuration** in `app/app.tsx`. The configuration in `AppNavigator.tsx` was being ignored.

### **2. Second Hypothesis: Auth State Not Updating**
- **Symptom:** Even with the `linking` prop, the app state (`isAuthenticated`) wasn't updating, keeping the user in the unauthenticated navigator stack.
- **Action:** Added an `onAuthStateChange` listener to the `AuthenticationStore` to react to the session token in the deep link URL.
- **Discovery:** This still didn't work. The console logs showed the listener was **never firing**, suggesting a race condition.

### **3. Third Hypothesis: Race Condition**
- **Symptom:** The `onAuthStateChange` event was likely firing before the listener in the MobX store was initialized.
- **Action:** Moved the `onAuthStateChange` listener to `setupRootStore.ts` to ensure it was attached as early as possible in the app's lifecycle.
- **Discovery:** The listener *still* wasn't firing. This led to a deeper investigation of the Supabase client itself.

### **4. Root Cause Discovery: `detectSessionInUrl`**
- **Symptom:** The Supabase client was not processing the URL fragment.
- **Action:** Inspected the client initialization in `app/supabase/index.ts`.
- **Discovery:** The root cause was found: **`detectSessionInUrl: false`**. This option explicitly instructed the Supabase client to ignore auth tokens in the URL, effectively disabling the deep link authentication flow.

### **5. Final Hurdle: Expo Development Client Crash**
- **Symptom:** After fixing the Supabase client, the user reported that receiving the deep link was causing the **Expo development client to crash and rebundle from scratch**, losing the link's context.
- **Action:** Inspected `app.json` and `app.config.ts`.
- **Discovery:** The Expo project configuration was not robust enough to reliably handle the deep link without crashing the development server.

## ✅ **Final Solution Implemented**

A combination of fixes was required to address all discovered issues:

1.  **Enabled URL Session Detection**:
    *   In `app/supabase/index.ts`, changed the client configuration to `detectSessionInUrl: true`. This was the most critical fix, allowing the client to process the token from the deep link.

2.  **Centralized the Auth Listener**:
    *   The `supabase.auth.onAuthStateChange` listener was placed in `app/models/helpers/setupRootStore.ts` to ensure it is registered early and can reliably catch the `SIGNED_IN` event triggered by the deep link.

3.  **Hardened Expo Configuration**:
    *   In `app.json`, the configuration was made more robust to prevent the development client from crashing:
        *   Added the **`expo-router`** plugin to improve deep link handling.
        *   Added an explicit **`intent-filter`** for Android.
        *   Added the `scheme` to the `ios` configuration for clarity.

4.  **Installed Missing Dependency**:
    *   Ran `npm install expo-router` to add the required package for the `expo-router` plugin, resolving the final `PluginError`.

## 🗂️ **Files Touched**
- `app/supabase/index.ts` (Core fix)
- `app/json` (Expo configuration hardening)
- `package.json` (Added `expo-router`)
- `app/models/helpers/setupRootStore.ts` (Centralized auth listener)
- `app/models/AuthenticationStore.ts` (Removed redundant listener)
- `app/app.tsx` (Consolidated `linking` config and removed for cleanup)
- `app/navigators/AppNavigator.tsx` (Removed duplicate `linking` config)

## ✨ **Result**
The password reset deep linking functionality is now fully operational. The combination of these fixes ensures that:
1. The Expo development client no longer crashes upon receiving a deep link.
2. The Supabase client correctly detects and processes the auth token in the URL.
3. The application's auth state is updated in a timely manner.
4. React Navigation successfully navigates the user to the `PasswordResetConfirm` screen.
